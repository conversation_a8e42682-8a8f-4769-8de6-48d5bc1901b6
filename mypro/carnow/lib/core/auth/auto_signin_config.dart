// =============================================================================
// AUTO SIGN-IN CONFIGURATION - Forever Plan Architecture
// =============================================================================
//
// تكوين التسجيل التلقائي - بنية الخطة الدائمة
// Enhanced auto sign-in configuration for seamless user experience
//
// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
// ✅ Automatic session restoration on app startup
// ✅ Google Sign-In lightweight authentication
// ✅ Token validation and refresh
// ✅ Graceful degradation on failure
// =============================================================================

import 'dart:developer' as developer;

/// Auto sign-in configuration
/// تكوين التسجيل التلقائي
class AutoSignInConfig {
  /// Enable automatic sign-in attempts
  /// تمكين محاولات التسجيل التلقائي
  static const bool enableAutoSignIn = true;

  /// Enable Google lightweight authentication
  /// تمكين المصادقة الخفيفة لـ Google
  static const bool enableGoogleLightweightAuth = true;

  /// Enable token-based session restoration
  /// تمكين استعادة الجلسة باستخدام الرموز المميزة
  static const bool enableTokenBasedRestore = true;

  /// Maximum retry attempts for auto sign-in
  /// الحد الأقصى لمحاولات إعادة المحاولة للتسجيل التلقائي
  static const int maxRetryAttempts = 3;

  /// Delay between retry attempts (in milliseconds)
  /// التأخير بين محاولات إعادة المحاولة (بالميلي ثانية)
  static const int retryDelayMs = 1000;

  /// Timeout for auto sign-in operations (in seconds)
  /// مهلة عمليات التسجيل التلقائي (بالثواني)
  static const int autoSignInTimeoutSeconds = 10;

  /// Enable background initialization
  /// تمكين التهيئة في الخلفية
  static const bool enableBackgroundInit = true;

  /// Show loading indicator during auto sign-in
  /// إظهار مؤشر التحميل أثناء التسجيل التلقائي
  static const bool showLoadingIndicator = true;

  /// Log auto sign-in attempts for debugging
  /// تسجيل محاولات التسجيل التلقائي للتصحيح
  static const bool enableDebugLogging = true;

  /// Auto sign-in priority order
  /// ترتيب أولوية التسجيل التلقائي
  static const List<AutoSignInMethod> priorityOrder = [
    AutoSignInMethod.tokenBased,
    AutoSignInMethod.googleLightweight,
  ];

  /// Check if auto sign-in is enabled
  /// التحقق من تمكين التسجيل التلقائي
  static bool get isEnabled => enableAutoSignIn;

  /// Check if Google lightweight auth is enabled
  /// التحقق من تمكين المصادقة الخفيفة لـ Google
  static bool get isGoogleLightweightEnabled => 
      enableAutoSignIn && enableGoogleLightweightAuth;

  /// Check if token-based restore is enabled
  /// التحقق من تمكين استعادة الجلسة باستخدام الرموز المميزة
  static bool get isTokenBasedRestoreEnabled => 
      enableAutoSignIn && enableTokenBasedRestore;

  /// Get retry delay duration
  /// الحصول على مدة تأخير إعادة المحاولة
  static Duration get retryDelay => Duration(milliseconds: retryDelayMs);

  /// Get auto sign-in timeout duration
  /// الحصول على مدة مهلة التسجيل التلقائي
  static Duration get timeout => Duration(seconds: autoSignInTimeoutSeconds);

  /// Log auto sign-in event
  /// تسجيل حدث التسجيل التلقائي
  static void logEvent(String message, {Object? error, StackTrace? stackTrace}) {
    if (enableDebugLogging) {
      developer.log(
        message,
        name: 'AutoSignInConfig',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Get next auto sign-in method to try
  /// الحصول على طريقة التسجيل التلقائي التالية للمحاولة
  static AutoSignInMethod? getNextMethod(List<AutoSignInMethod> attempted) {
    for (final method in priorityOrder) {
      if (!attempted.contains(method)) {
        return method;
      }
    }
    return null;
  }

  /// Check if method is enabled
  /// التحقق من تمكين الطريقة
  static bool isMethodEnabled(AutoSignInMethod method) {
    switch (method) {
      case AutoSignInMethod.tokenBased:
        return isTokenBasedRestoreEnabled;
      case AutoSignInMethod.googleLightweight:
        return isGoogleLightweightEnabled;
    }
  }
}

/// Auto sign-in methods
/// طرق التسجيل التلقائي
enum AutoSignInMethod {
  /// Token-based session restoration
  /// استعادة الجلسة باستخدام الرموز المميزة
  tokenBased,

  /// Google lightweight authentication
  /// المصادقة الخفيفة لـ Google
  googleLightweight,
}

/// Auto sign-in result
/// نتيجة التسجيل التلقائي
sealed class AutoSignInResult {
  const AutoSignInResult();

  /// Successful auto sign-in
  /// تسجيل تلقائي ناجح
  const factory AutoSignInResult.success({
    required AutoSignInMethod method,
    required String accessToken,
    String? refreshToken,
    Map<String, dynamic>? userData,
  }) = AutoSignInResultSuccess;

  /// Auto sign-in failed
  /// فشل التسجيل التلقائي
  const factory AutoSignInResult.failure({
    required AutoSignInMethod method,
    required String error,
    bool canRetry = false,
  }) = AutoSignInResultFailure;

  /// Auto sign-in skipped
  /// تم تخطي التسجيل التلقائي
  const factory AutoSignInResult.skipped({
    required String reason,
  }) = AutoSignInResultSkipped;

  /// Check if result is successful
  /// التحقق من نجاح النتيجة
  bool get isSuccess => this is AutoSignInResultSuccess;

  /// Check if result is failure
  /// التحقق من فشل النتيجة
  bool get isFailure => this is AutoSignInResultFailure;

  /// Check if result is skipped
  /// التحقق من تخطي النتيجة
  bool get isSkipped => this is AutoSignInResultSkipped;

  /// Get method used (null for skipped)
  /// الحصول على الطريقة المستخدمة (null للمتخطى)
  AutoSignInMethod? get method => switch (this) {
    AutoSignInResultSuccess(method: final m) => m,
    AutoSignInResultFailure(method: final m) => m,
    AutoSignInResultSkipped() => null,
  };

  /// Get error message (null for success)
  /// الحصول على رسالة الخطأ (null للنجاح)
  String? get error => switch (this) {
    AutoSignInResultSuccess() => null,
    AutoSignInResultFailure(error: final e) => e,
    AutoSignInResultSkipped(reason: final r) => r,
  };
}

/// Successful auto sign-in result
/// نتيجة التسجيل التلقائي الناجح
class AutoSignInResultSuccess extends AutoSignInResult {
  final AutoSignInMethod method;
  final String accessToken;
  final String? refreshToken;
  final Map<String, dynamic>? userData;

  const AutoSignInResultSuccess({
    required this.method,
    required this.accessToken,
    this.refreshToken,
    this.userData,
  });
}

/// Failed auto sign-in result
/// نتيجة التسجيل التلقائي الفاشل
class AutoSignInResultFailure extends AutoSignInResult {
  final AutoSignInMethod method;
  final String error;
  final bool canRetry;

  const AutoSignInResultFailure({
    required this.method,
    required this.error,
    this.canRetry = false,
  });
}

/// Skipped auto sign-in result
/// نتيجة التسجيل التلقائي المتخطى
class AutoSignInResultSkipped extends AutoSignInResult {
  final String reason;

  const AutoSignInResultSkipped({
    required this.reason,
  });
}

/// Auto sign-in state
/// حالة التسجيل التلقائي
enum AutoSignInState {
  /// Not started
  /// لم يبدأ
  notStarted,

  /// In progress
  /// قيد التقدم
  inProgress,

  /// Completed successfully
  /// اكتمل بنجاح
  completed,

  /// Failed
  /// فشل
  failed,

  /// Skipped
  /// متخطى
  skipped,
}

/// Auto sign-in statistics
/// إحصائيات التسجيل التلقائي
class AutoSignInStats {
  final int totalAttempts;
  final int successfulAttempts;
  final int failedAttempts;
  final int skippedAttempts;
  final Map<AutoSignInMethod, int> methodSuccessCount;
  final Map<AutoSignInMethod, int> methodFailureCount;
  final DateTime? lastSuccessfulSignIn;
  final DateTime? lastFailedSignIn;

  const AutoSignInStats({
    this.totalAttempts = 0,
    this.successfulAttempts = 0,
    this.failedAttempts = 0,
    this.skippedAttempts = 0,
    this.methodSuccessCount = const {},
    this.methodFailureCount = const {},
    this.lastSuccessfulSignIn,
    this.lastFailedSignIn,
  });

  /// Success rate as percentage
  /// معدل النجاح كنسبة مئوية
  double get successRate {
    if (totalAttempts == 0) return 0.0;
    return (successfulAttempts / totalAttempts) * 100;
  }

  /// Most successful method
  /// الطريقة الأكثر نجاحاً
  AutoSignInMethod? get mostSuccessfulMethod {
    if (methodSuccessCount.isEmpty) return null;
    
    return methodSuccessCount.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Copy with updated values
  /// نسخ مع قيم محدثة
  AutoSignInStats copyWith({
    int? totalAttempts,
    int? successfulAttempts,
    int? failedAttempts,
    int? skippedAttempts,
    Map<AutoSignInMethod, int>? methodSuccessCount,
    Map<AutoSignInMethod, int>? methodFailureCount,
    DateTime? lastSuccessfulSignIn,
    DateTime? lastFailedSignIn,
  }) {
    return AutoSignInStats(
      totalAttempts: totalAttempts ?? this.totalAttempts,
      successfulAttempts: successfulAttempts ?? this.successfulAttempts,
      failedAttempts: failedAttempts ?? this.failedAttempts,
      skippedAttempts: skippedAttempts ?? this.skippedAttempts,
      methodSuccessCount: methodSuccessCount ?? this.methodSuccessCount,
      methodFailureCount: methodFailureCount ?? this.methodFailureCount,
      lastSuccessfulSignIn: lastSuccessfulSignIn ?? this.lastSuccessfulSignIn,
      lastFailedSignIn: lastFailedSignIn ?? this.lastFailedSignIn,
    );
  }
}
