import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'enhanced_secure_token_storage.dart';
import 'auth_interfaces.dart';
import 'session_persistence_diagnostics.dart';
import '../config/session_persistence_config.dart';

part 'session_recovery_service.g.dart';

/// Session Recovery Service
/// خدمة استرداد الجلسة
/// 
/// This service handles session recovery and persistence issues following Forever Plan Architecture:
/// - Real data only from Supabase database
/// - No mock data tolerance
/// - Comprehensive error handling and recovery
/// - Production-ready session management
class SessionRecoveryService {
  final ITokenStorage _tokenStorage;
  final SessionPersistenceDiagnostics _diagnostics;

  SessionRecoveryService({
    required ITokenStorage tokenStorage,
    required SessionPersistenceDiagnostics diagnostics,
  }) : _tokenStorage = tokenStorage,
       _diagnostics = diagnostics;

  /// Attempt to recover session with comprehensive error handling and Google Auth
  /// محاولة استرداد الجلسة مع معالجة شاملة للأخطاء ومصادقة Google
  Future<SessionRecoveryResult> attemptSessionRecovery() async {
    developer.log('🔄 Starting enhanced session recovery process...', name: 'SessionRecoveryService');

    try {
      // Step 1: Run diagnostics to understand the current state
      final diagnosticsResult = await _diagnostics.runDiagnostics();

      developer.log(
        'Diagnostics completed: healthy=${diagnosticsResult.isHealthy}, issues=${diagnosticsResult.issues.length}',
        name: 'SessionRecoveryService',
      );

      // Step 2: Try token-based recovery first
      final tokenRecoveryResult = await _attemptTokenBasedRecovery();
      if (tokenRecoveryResult.isSuccess) {
        developer.log('✅ Token-based session recovery successful', name: 'SessionRecoveryService');
        return tokenRecoveryResult;
      }

      // Step 3: Try Google lightweight authentication if token recovery failed
      final googleRecoveryResult = await _attemptGoogleLightweightRecovery();
      if (googleRecoveryResult.isSuccess) {
        developer.log('✅ Google lightweight session recovery successful', name: 'SessionRecoveryService');
        return googleRecoveryResult;
      }

      // Step 4: If both methods fail, return comprehensive failure info
      return SessionRecoveryResult.failure(
        error: 'No recoverable session found',
        details: {
          'token_recovery_error': tokenRecoveryResult.error,
          'google_recovery_error': googleRecoveryResult.error,
          'diagnostics_healthy': diagnosticsResult.isHealthy,
          'diagnostics_issues': diagnosticsResult.issues.length,
        },
      );

    } catch (error, stackTrace) {
      developer.log(
        'Session recovery failed with unexpected error: $error',
        name: 'SessionRecoveryService',
        error: error,
        stackTrace: stackTrace,
      );

      return SessionRecoveryResult.failure(
        error: 'Unexpected error during session recovery: $error',
        details: {'stack_trace': stackTrace.toString()},
      );
    }
  }

  /// Attempt token-based session recovery
  /// محاولة استرداد الجلسة باستخدام الرموز المخزنة
  Future<SessionRecoveryResult> _attemptTokenBasedRecovery() async {
    try {
      developer.log('🔑 Attempting token-based recovery...', name: 'SessionRecoveryService');

      // Check if we have valid tokens
      final hasValidToken = await _tokenStorage.hasValidToken();
      if (!hasValidToken) {
        return SessionRecoveryResult.failure(
          error: 'No valid tokens found',
          details: {'method': 'token_based'},
        );
      }

      // Get tokens and session data
      final accessToken = await _tokenStorage.getToken();
      final refreshToken = await _tokenStorage.getRefreshToken();
      final sessionData = await _tokenStorage.getSessionData();

      if (accessToken != null && sessionData.isNotEmpty && sessionData['user'] != null) {
        return SessionRecoveryResult.success(
          accessToken: accessToken,
          refreshToken: refreshToken,
          recoveryMethod: 'enhanced_token_based',
        );
      } else {
        return SessionRecoveryResult.failure(
          error: 'Incomplete session data - missing access token or user data',
          recoveryMethod: 'enhanced_token_based',
          requiresReauthentication: true,
        );
      }

    } catch (error) {
      developer.log(
        'Token-based recovery failed: $error',
        name: 'SessionRecoveryService',
        error: error,
      );
      return SessionRecoveryResult.failure(
        error: 'Token recovery error: $error',
        recoveryMethod: 'enhanced_token_based',
        requiresReauthentication: true,
      );
    }
  }

  /// Attempt Google lightweight authentication recovery
  /// محاولة استرداد الجلسة باستخدام المصادقة الخفيفة لـ Google
  Future<SessionRecoveryResult> _attemptGoogleLightweightRecovery() async {
    try {
      developer.log('🔍 Attempting Google lightweight recovery...', name: 'SessionRecoveryService');

      // This would require integration with GoogleAuthService
      // For now, return a placeholder indicating this method is available
      return SessionRecoveryResult.failure(
        error: 'Google lightweight recovery not yet implemented',
        recoveryMethod: 'enhanced_google_lightweight',
        requiresReauthentication: false,
      );

    } catch (error) {
      developer.log(
        'Google lightweight recovery failed: $error',
        name: 'SessionRecoveryService',
        error: error,
      );
      return SessionRecoveryResult.failure(
        error: 'Google recovery error: $error',
        recoveryMethod: 'enhanced_google_lightweight',
        requiresReauthentication: true,
      );
    }
  }

  /// Legacy method for compatibility - attempt recovery based on diagnostics
  /// طريقة قديمة للتوافق - محاولة الاسترداد بناءً على التشخيص
  Future<SessionRecoveryResult> _attemptRecoveryBasedOnIssues(dynamic diagnosticsResult) async {
    try {
      developer.log('🔧 Attempting legacy recovery based on diagnostics...', name: 'SessionRecoveryService');

      // This is a fallback method for the existing implementation
      return SessionRecoveryResult.failure(
        error: 'Legacy recovery method - use enhanced recovery instead',
        recoveryMethod: 'legacy_diagnostics_based',
        requiresReauthentication: false,
      );

    } catch (error) {
      developer.log(
        'Legacy recovery method failed: $error',
        name: 'SessionRecoveryService',
        error: error,
      );

      return SessionRecoveryResult.failure(
        error: 'Legacy recovery error: $error',
        recoveryMethod: 'legacy_diagnostics_based',
        requiresReauthentication: true,
      );
    }
  }
}

/// Session recovery result
class SessionRecoveryResult {

  /// Handle token expiry issues
  Future<SessionRecoveryResult> _handleTokenExpiryIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('⏰ Handling token expiry issues...', name: 'SessionRecoveryService');
    
    try {
      final refreshToken = await _tokenStorage.getRefreshToken();
      
      if (refreshToken != null) {
        developer.log('🔄 Refresh token available for token renewal', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.refreshRequired(
          refreshToken: refreshToken,
          recoveryMethod: 'token_refresh',
          diagnostics: diagnosticsResult,
        );
      } else {
        developer.log('❌ No refresh token available', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.failure(
          error: 'Access token expired and no refresh token available',
          recoveryMethod: 'token_expiry_no_refresh',
          requiresReauthentication: true,
          diagnostics: diagnosticsResult,
        );
      }
    } catch (e) {
      developer.log('❌ Failed to handle token expiry: $e', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Failed to handle token expiry: $e',
        recoveryMethod: 'token_expiry_error',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    }
  }

  /// Handle storage integrity issues
  Future<SessionRecoveryResult> _handleStorageIntegrityIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('🔒 Handling storage integrity issues...', name: 'SessionRecoveryService');
    
    try {
      // Clear all data to reset integrity
      await _tokenStorage.clearAllData();
      
      developer.log('✅ Storage integrity reset completed', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Storage integrity was compromised and has been reset',
        recoveryMethod: 'integrity_reset',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    } catch (e) {
      developer.log('❌ Failed to reset storage integrity: $e', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Failed to reset storage integrity: $e',
        recoveryMethod: 'integrity_reset_failed',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    }
  }

  /// Handle missing tokens issues
  Future<SessionRecoveryResult> _handleMissingTokensIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('📭 Handling missing tokens issues...', name: 'SessionRecoveryService');
    
    return SessionRecoveryResult.failure(
      error: 'No authentication tokens found in storage',
      recoveryMethod: 'missing_tokens',
      requiresReauthentication: true,
      diagnostics: diagnosticsResult,
    );
  }

  /// Attempt default recovery
  Future<SessionRecoveryResult> _attemptDefaultRecovery(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('🔄 Attempting default recovery...', name: 'SessionRecoveryService');
    
    try {
      // Try to get tokens one more time
      final accessToken = await _tokenStorage.getToken();
      final refreshToken = await _tokenStorage.getRefreshToken();
      
      if (accessToken != null) {
        developer.log('✅ Default recovery successful', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.success(
          accessToken: accessToken,
          refreshToken: refreshToken,
          recoveryMethod: 'default_recovery',
          diagnostics: diagnosticsResult,
        );
      } else if (refreshToken != null) {
        developer.log('🔄 Default recovery requires token refresh', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.refreshRequired(
          refreshToken: refreshToken,
          recoveryMethod: 'default_recovery_refresh',
          diagnostics: diagnosticsResult,
        );
      } else {
        developer.log('❌ Default recovery failed - no tokens available', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.failure(
          error: 'No valid tokens found after recovery attempts',
          recoveryMethod: 'default_recovery_failed',
          requiresReauthentication: true,
          diagnostics: diagnosticsResult,
        );
      }
    } catch (e) {
      developer.log('❌ Default recovery error: $e', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Default recovery failed: $e',
        recoveryMethod: 'default_recovery_error',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    }
  }

  /// Force clear all session data (for troubleshooting)
  Future<void> forceClearSession() async {
    developer.log('🧹 Force clearing all session data...', name: 'SessionRecoveryService');
    
    try {
      await _tokenStorage.clearAllData();
      developer.log('✅ Session force clear completed', name: 'SessionRecoveryService');
    } catch (e) {
      developer.log('❌ Failed to force clear session: $e', name: 'SessionRecoveryService');
      throw Exception('Failed to force clear session: $e');
    }
  }
}

/// Session recovery result
class SessionRecoveryResult {
  final bool isSuccess;
  final String? accessToken;
  final String? refreshToken;
  final bool requiresRefresh;
  final bool requiresReauthentication;
  final String? error;
  final String recoveryMethod;
  final SessionDiagnosticsResult? diagnostics;

  const SessionRecoveryResult._({
    required this.isSuccess,
    this.accessToken,
    this.refreshToken,
    this.requiresRefresh = false,
    this.requiresReauthentication = false,
    this.error,
    required this.recoveryMethod,
    this.diagnostics,
  });

  /// Successful recovery with valid tokens
  factory SessionRecoveryResult.success({
    required String accessToken,
    String? refreshToken,
    required String recoveryMethod,
    SessionDiagnosticsResult? diagnostics,
  }) {
    return SessionRecoveryResult._(
      isSuccess: true,
      accessToken: accessToken,
      refreshToken: refreshToken,
      recoveryMethod: recoveryMethod,
      diagnostics: diagnostics,
    );
  }

  /// Recovery requires token refresh
  factory SessionRecoveryResult.refreshRequired({
    required String refreshToken,
    required String recoveryMethod,
    SessionDiagnosticsResult? diagnostics,
  }) {
    return SessionRecoveryResult._(
      isSuccess: false,
      refreshToken: refreshToken,
      requiresRefresh: true,
      recoveryMethod: recoveryMethod,
      diagnostics: diagnostics,
    );
  }

  /// Recovery failed, requires reauthentication
  factory SessionRecoveryResult.failure({
    required String error,
    required String recoveryMethod,
    required bool requiresReauthentication,
    SessionDiagnosticsResult? diagnostics,
  }) {
    return SessionRecoveryResult._(
      isSuccess: false,
      error: error,
      requiresReauthentication: requiresReauthentication,
      recoveryMethod: recoveryMethod,
      diagnostics: diagnostics,
    );
  }

  @override
  String toString() {
    return 'SessionRecoveryResult(success: $isSuccess, method: $recoveryMethod, requiresAuth: $requiresReauthentication)';
  }
}

/// Provider for session recovery service
@riverpod
SessionRecoveryService sessionRecoveryService(Ref ref) {
  final tokenStorage = ref.read(enhancedSecureTokenStorageProvider);
  final diagnostics = ref.read(sessionPersistenceDiagnosticsProvider);
  return SessionRecoveryService(
    tokenStorage: tokenStorage,
    diagnostics: diagnostics,
  );
}
