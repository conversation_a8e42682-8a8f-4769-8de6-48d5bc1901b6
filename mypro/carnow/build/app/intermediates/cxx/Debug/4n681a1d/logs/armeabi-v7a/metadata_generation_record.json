[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: armeabi-v7a", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a/android_gradle_build.json due to:", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- a hard configure file changed, will remove stale configuration folder", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - /Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a/metadata_generation_command.txt (LAST_MODIFIED_CHANGED)", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a'", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a'", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=21 \\\n  -DANDROID_PLATFORM=android-21 \\\n  -DANDROID_ABI=armeabi-v7a \\\n  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/armeabi-v7a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/armeabi-v7a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a \\\n  -GNinja \\\n  -Wno-dev \\\n  --no-warn-unused-cli\n", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=21 \\\n  -DANDROID_PLATFORM=android-21 \\\n  -DANDROID_ABI=armeabi-v7a \\\n  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/armeabi-v7a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/armeabi-v7a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a \\\n  -GNinja \\\n  -Wno-dev \\\n  --no-warn-unused-cli\n", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a/compile_commands.json.bin existed but not /Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/armeabi-v7a/compile_commands.json", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]